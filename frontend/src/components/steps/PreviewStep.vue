<template>
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">预览文件内容</h2>
      <p class="text-lg text-gray-600">
        确认文件内容无误后，点击下一步进行翻译配置
      </p>
    </div>

    <!-- 文件信息卡片 -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8"
    >
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-100"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div
              class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center"
            >
              <font-awesome-icon icon="file-excel" class="text-white text-xl" />
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">
                {{ excelData?.metadata.filename }}
              </h3>
              <p class="text-gray-600">
                工作表: {{ excelData?.metadata.sheet_name }}
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">
                {{ excelData?.languages.length }}
              </div>
              <div class="text-sm text-gray-600">语言</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">
                {{ excelData?.metadata.total_keys }}
              </div>
              <div class="text-sm text-gray-600">条目</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 语言列表 -->
      <div class="p-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">检测到的语言</h4>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div
            v-for="language in excelData?.languages"
            :key="language"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200"
          >
            <div
              class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
            >
              <font-awesome-icon icon="globe" class="text-blue-600 text-sm" />
            </div>
            <span class="font-medium text-gray-900">{{ language }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据预览表格 -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
    >
      <div class="px-8 py-6 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <h4 class="text-lg font-semibold text-gray-900">数据预览</h4>
          <div class="flex items-center space-x-2 text-sm text-gray-600">
            <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
            <span>/</span>
            <span>共 {{ totalItems }} 条记录</span>
          </div>
        </div>
      </div>

      <!-- 固定表格容器 -->
      <div class="relative max-h-96 overflow-auto">
        <table class="w-full border-collapse">
          <!-- 固定表头 -->
          <thead class="bg-gray-50 sticky top-0 z-20">
            <tr>
              <!-- 固定的左上角单元格 -->
              <th
                class="sticky left-0 z-30 bg-gray-50 px-4 py-3 text-left text-sm font-semibold text-gray-900 border-r border-b border-gray-200 min-w-[100px] max-w-[200px]"
              >
                <div class="truncate">翻译键</div>
              </th>
              <!-- 语言列标题 -->
              <th
                v-for="language in excelData?.languages"
                :key="language"
                class="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-r border-b border-gray-200 last:border-r-0 min-w-[100px] max-w-[200px] bg-gray-50"
              >
                <div class="truncate" :title="language">{{ language }}</div>
              </th>
            </tr>
          </thead>
          <!-- 表格主体 -->
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="(translations, key, index) in limitedTranslations"
              :key="key"
              :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
            >
              <!-- 固定的第一列 -->
              <td
                class="sticky left-0 z-10 px-4 py-3 text-sm font-medium text-gray-900 border-r border-gray-200 min-w-[100px] max-w-[200px]"
                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
              >
                <div class="truncate" :title="String(key)">{{ key }}</div>
              </td>
              <!-- 数据单元格 -->
              <td
                v-for="language in excelData?.languages"
                :key="language"
                class="px-4 py-3 text-sm text-gray-700 border-r border-gray-200 last:border-r-0 min-w-[100px] max-w-[200px]"
              >
                <div class="truncate" :title="translations[language] || '-'">
                  {{ translations[language] || '-' }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页组件 -->
      <div
        v-if="totalPages > 1"
        class="px-8 py-4 bg-gray-50 border-t border-gray-200"
      >
        <div class="flex items-center justify-center space-x-2">
          <!-- 上一页按钮 -->
          <button
            @click="prevPage"
            :disabled="currentPage === 1"
            :class="[
              'inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
              currentPage === 1
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50',
            ]"
          >
            <font-awesome-icon icon="chevron-left" class="mr-1" />
            上一页
          </button>

          <!-- 页码按钮 -->
          <div class="flex items-center space-x-1">
            <!-- 第一页 -->
            <button
              v-if="currentPage > 3"
              @click="goToPage(1)"
              class="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
            >
              1
            </button>

            <!-- 省略号 -->
            <span v-if="currentPage > 4" class="px-2 text-gray-400">...</span>

            <!-- 当前页前后的页码 -->
            <button
              v-for="page in getVisiblePages()"
              :key="page"
              @click="goToPage(page)"
              :class="[
                'inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-colors duration-200',
                page === currentPage
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50',
              ]"
            >
              {{ page }}
            </button>

            <!-- 省略号 -->
            <span v-if="currentPage < totalPages - 3" class="px-2 text-gray-400"
              >...</span
            >

            <!-- 最后一页 -->
            <button
              v-if="currentPage < totalPages - 2"
              @click="goToPage(totalPages)"
              class="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
            >
              {{ totalPages }}
            </button>
          </div>

          <!-- 下一页按钮 -->
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            :class="[
              'inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
              currentPage === totalPages
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50',
            ]"
          >
            下一页
            <font-awesome-icon icon="chevron-right" class="ml-1" />
          </button>
        </div>

        <!-- 分页信息 -->
        <div class="text-center mt-3 text-sm text-gray-600">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} -
          {{ Math.min(currentPage * pageSize, totalItems) }} 条，共
          {{ totalItems }} 条记录
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between mt-8">
      <button
        @click="$emit('back')"
        class="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200"
      >
        <font-awesome-icon icon="arrow-left" class="mr-2" />
        重新上传
      </button>

      <button
        @click="$emit('confirm')"
        class="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
      >
        确认无误，下一步
        <font-awesome-icon icon="arrow-right" class="ml-2" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { ExcelData } from '@/types';

const props = defineProps<{
  excelData: ExcelData | null;
}>();

const emit = defineEmits<{
  confirm: [];
  back: [];
  restart: [];
}>();

// 响应式数据
const currentPage = ref(1);
const pageSize = ref(10);

// 计算属性
const totalItems = computed(() => {
  if (!props.excelData) return 0;
  return Object.keys(props.excelData.translations).length;
});

const totalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value);
});

const limitedTranslations = computed(() => {
  if (!props.excelData) return {};

  const entries = Object.entries(props.excelData.translations);
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  const limited = entries.slice(startIndex, endIndex);
  return Object.fromEntries(limited);
});

// 分页操作
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 获取可见的页码列表
const getVisiblePages = () => {
  const pages: number[] = [];
  const current = currentPage.value;
  const total = totalPages.value;

  // 显示当前页前后各2页
  const start = Math.max(1, current - 2);
  const end = Math.min(total, current + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
};
</script>
