<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">上传 Excel 文件</h2>
      <p class="text-lg text-gray-600">
        支持 .xlsx 和 .xls 格式，文件大小不超过 10MB
      </p>
    </div>

    <!-- 文件上传区域 -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
    >
      <!-- 拖拽上传区域 -->
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        @dragleave="isDragOver = false"
        @dragover="isDragOver = true"
        :class="[
          'relative p-12 border-2 border-dashed transition-all duration-300 cursor-pointer rounded-2xl',
          isDragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50',
        ]"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept=".xlsx,.xls"
          @change="handleFileSelect"
          class="hidden"
        />

        <div class="text-center">
          <div class="mb-6">
            <div
              :class="[
                'w-20 h-20 mx-auto rounded-full flex items-center justify-center transition-all duration-300',
                isDragOver
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-400',
              ]"
            >
              <font-awesome-icon
                :icon="isDragOver ? 'upload' : 'file-excel'"
                class="text-3xl"
              />
            </div>
          </div>

          <div class="space-y-2">
            <p class="text-xl font-semibold text-gray-900">
              {{ isDragOver ? '释放文件以上传' : '拖拽文件到此处或点击上传' }}
            </p>
            <p class="text-gray-500">支持 Excel 文件格式 (.xlsx, .xls)</p>
          </div>

          <div class="mt-8">
            <button
              :disabled="loading"
              class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
            >
              <font-awesome-icon
                :icon="loading ? 'spinner' : 'upload'"
                :class="['mr-2', loading ? 'animate-spin' : '']"
              />
              {{ loading ? '上传中...' : '选择文件' }}
            </button>
          </div>
        </div>

        <!-- 加载遮罩 -->
        <div
          v-if="loading"
          class="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center"
        >
          <div class="text-center">
            <div class="w-12 h-12 mx-auto mb-4">
              <font-awesome-icon
                icon="spinner"
                class="text-3xl text-blue-600 animate-spin"
              />
            </div>
            <p class="text-lg font-medium text-gray-900">正在处理文件...</p>
            <p class="text-sm text-gray-500 mt-1">请稍候</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件格式说明 -->
    <div class="mt-8 bg-blue-50 rounded-xl p-6 border border-blue-100">
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <font-awesome-icon
            icon="info-circle"
            class="text-blue-600 text-lg mt-0.5"
          />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-blue-900 mb-2">文件格式要求</h3>
          <ul class="space-y-1 text-blue-800">
            <li class="flex items-center space-x-2">
              <font-awesome-icon icon="check" class="text-green-600 text-sm" />
              <span>第一列为翻译键名（Key）</span>
            </li>
            <li class="flex items-center space-x-2">
              <font-awesome-icon icon="check" class="text-green-600 text-sm" />
              <span>其他列为各语言翻译内容</span>
            </li>
            <li class="flex items-center space-x-2">
              <font-awesome-icon icon="check" class="text-green-600 text-sm" />
              <span>第一行为列标题（语言代码）</span>
            </li>
            <li class="flex items-center space-x-2">
              <font-awesome-icon icon="check" class="text-green-600 text-sm" />
              <span>文件大小不超过 10MB</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { translateApiService } from '@/api/translate/service';
import type { ExcelData } from '@/types';

const emit = defineEmits<{
  fileUploaded: [data: ExcelData];
}>();

const props = defineProps<{
  loading: boolean;
}>();

// 响应式数据
const isDragOver = ref(false);
const fileInput = ref<HTMLInputElement>();

// 触发文件选择
const triggerFileInput = () => {
  if (!props.loading) {
    fileInput.value?.click();
  }
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    processFile(file);
  }
};

// 处理拖拽上传
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (
      file.type.includes('sheet') ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls')
    ) {
      processFile(file);
    } else {
      alert('请上传 Excel 文件 (.xlsx 或 .xls)');
    }
  }
};

// 处理文件
const processFile = async (file: File) => {
  try {
    // request.helper.ts 已经处理了 API 响应格式
    // 成功时直接返回 data 字段内容，失败时抛出错误
    const apiData = await translateApiService.uploadFile(file);
    console.log('Upload success:', apiData);

    // 转换 API 数据格式为页面组件期望的格式
    // API 返回的字段名是 translations，页面组件期望的是 translationObject
    const pageData: ExcelData = {
      languages: apiData.languages,
      translationObject: apiData.translations,
      metadata: apiData.metadata,
    };

    emit('fileUploaded', pageData);
  } catch (error) {
    console.error('Upload error:', error);
    alert('文件上传失败，请重试');
  }
};
</script>
