/**
 * 翻译模块API类型定义
 */

// ============= 通用API响应类型 =============
export interface Response<T = any> {
  code: number;
  data?: T;
  error?: object;
  message?: string;
}

// ============= API请求状态枚举 =============
export enum ApiStatus {
  SUCCESS = 0,
  ERROR = 1
}

// ============= 翻译状态枚举 =============
export enum TranslationStatus {
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// ============= 翻译配置枚举 =============
export enum TranslationStyle {
  FORMAL = 'formal',
  INFORMAL = 'informal',
  CASUAL = 'casual'
}

export enum TranslationDomain {
  GENERAL = 'general',
  TECHNICAL = 'technical',
  BUSINESS = 'business'
}

// ============= Excel上传相关类型 =============
export interface ExcelUploadRequest {
  file: File
}

export interface ExcelMetadata {
  filename: string
  total_keys: number
  total_languages: number
  sheet_name: string
}

export interface ExcelData {
  languages: string[]
  translations: Record<string, Record<string, string>>
  metadata: ExcelMetadata
}

export interface ExcelUploadResponse {
  success: boolean
  data?: ExcelData
  error?: string
}

// ============= 批量翻译相关类型 =============
export interface TranslationConfig {
  style: TranslationStyle
  domain: TranslationDomain
  preserve_formatting: boolean
}

export interface TranslationItem {
  id: number
  text: string
  target_languages: string[]
}

export interface BatchTranslateRequest {
  data: TranslationItem[]
  config: TranslationConfig
}

export interface BatchTranslateResponse {
  task_id: string
  status: string
  message: string
  total_translations: number
}

// ============= 任务状态相关类型 =============
export interface TranslationTask {
  id: string
  status: TranslationStatus
  total_records: number
  completed_records: number
  failed_records: number
  config: TranslationConfig
  created_at: string
  updated_at: string
}

export interface TranslationHistoryResponse {
  history: TranslationTask[]
}

// ============= 翻译结果相关类型 =============
export interface TranslationResultItem {
  target_language: string
  translated_text?: string
  status: TranslationStatus | 'pending'
  error_message?: string
}

export interface TranslationResult {
  source_id: number
  source_text: string
  translations: TranslationResultItem[]
}

export interface TranslationResultsResponse {
  task_id: string
  results: TranslationResult[]
}

// ============= 单条翻译相关类型 =============
export interface TranslateRequest {
  text: string
  source_lang?: string
  target_lang: string
}

export interface TranslateResponse {
  success: boolean
  translated_text?: string
  error?: string
  source_lang?: string
  target_lang?: string
}

